package com.sun.englishlearning.utils

import android.content.Context
import android.util.Log
import com.google.firebase.firestore.ktx.firestore
import com.google.firebase.ktx.Firebase
import com.sun.englishlearning.data.model.Word
import kotlinx.coroutines.tasks.await
import org.json.JSONObject
import java.io.IOException

/**
 * Utility class to populate Firebase Firestore with sample vocabulary data
 * This is for development/testing purposes only
 */
class FirebaseDataPopulator {
    
    companion object {
        private const val TAG = "FirebaseDataPopulator"
        private const val VOCABULARY_FILE = "vocabulary.json"
    }
    
    private val db = Firebase.firestore
    
    /**
     * Populate Firebase with vocabulary data from assets/vocabulary.json
     * This creates Word documents in the "words" collection
     */
    suspend fun populateVocabularyData(context: Context): Result<Unit> {
        return try {
            Log.d(TAG, "Starting vocabulary data population...")
            
            // Load vocabulary data from assets
            val vocabularyData = loadVocabularyFromAssets(context)
            if (vocabularyData.isEmpty()) {
                return Result.failure(Exception("No vocabulary data found in assets"))
            }
            
            var totalWordsAdded = 0
            
            // Process each lesson's vocabulary
            vocabularyData.forEach { (lessonId, lessonData) ->
                val lessonTitle = lessonData.getString("lessonTitle")
                val words = lessonData.getJSONArray("words")
                
                Log.d(TAG, "Processing lesson $lessonId: $lessonTitle with ${words.length()} words")
                
                // Create Word objects for each vocabulary word
                for (i in 0 until words.length()) {
                    val wordText = words.getString(i)
                    val word = createSampleWord(wordText, lessonId, lessonTitle)
                    
                    // Add to Firebase
                    try {
                        db.collection("words")
                            .document(word.id)
                            .set(word)
                            .await()
                        
                        totalWordsAdded++
                        Log.d(TAG, "Added word: ${word.word} for lesson $lessonId")
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to add word: $wordText", e)
                    }
                }
            }
            
            Log.d(TAG, "Successfully populated $totalWordsAdded words to Firebase")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error populating vocabulary data", e)
            Result.failure(e)
        }
    }
    
    /**
     * Load vocabulary data from assets/vocabulary.json
     */
    private fun loadVocabularyFromAssets(context: Context): Map<String, JSONObject> {
        return try {
            val jsonString = context.assets.open(VOCABULARY_FILE).bufferedReader().use { it.readText() }
            val jsonObject = JSONObject(jsonString)
            val vocabularyObject = jsonObject.getJSONObject("vocabulary")
            
            val result = mutableMapOf<String, JSONObject>()
            vocabularyObject.keys().forEach { lessonId ->
                result[lessonId] = vocabularyObject.getJSONObject(lessonId)
            }
            
            result
        } catch (e: IOException) {
            Log.e(TAG, "Error loading vocabulary from assets", e)
            emptyMap()
        }
    }
    
    /**
     * Create a sample Word object with basic data
     * In a real app, you would fetch definitions from a dictionary API
     */
    private fun createSampleWord(wordText: String, lessonId: String, lessonTitle: String): Word {
        return Word(
            id = "${lessonId}_${wordText.lowercase().replace(" ", "_")}",
            word = wordText,
            definition = "Definition for $wordText", // Placeholder - would come from dictionary API
            pronunciation = "/${wordText.lowercase()}/", // Placeholder pronunciation
            phonetic = wordText.lowercase(),
            partOfSpeech = "noun", // Default part of speech
            example = "This is an example sentence using $wordText.",
            soundUrl = "", // Would be populated with actual audio URLs
            imageUrl = "", // Would be populated with actual image URLs
            lessonId = lessonId,
            difficulty = "easy"
        )
    }
    
    /**
     * Clear all vocabulary data from Firebase (for testing)
     */
    suspend fun clearVocabularyData(): Result<Unit> {
        return try {
            Log.d(TAG, "Clearing all vocabulary data from Firebase...")
            
            val snapshot = db.collection("words").get().await()
            val batch = db.batch()
            
            snapshot.documents.forEach { document ->
                batch.delete(document.reference)
            }
            
            batch.commit().await()
            Log.d(TAG, "Successfully cleared ${snapshot.documents.size} words from Firebase")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing vocabulary data", e)
            Result.failure(e)
        }
    }
    
    /**
     * Check if vocabulary data exists in Firebase
     */
    suspend fun checkVocabularyDataExists(): Result<Boolean> {
        return try {
            val snapshot = db.collection("words").limit(1).get().await()
            Result.success(snapshot.documents.isNotEmpty())
        } catch (e: Exception) {
            Log.e(TAG, "Error checking vocabulary data", e)
            Result.failure(e)
        }
    }
}
