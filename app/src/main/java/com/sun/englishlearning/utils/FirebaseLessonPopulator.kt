package com.sun.englishlearning.utils

import android.content.Context
import android.util.Log
import com.google.firebase.firestore.ktx.firestore
import com.google.firebase.ktx.Firebase
import com.sun.englishlearning.data.model.Lesson
import com.sun.englishlearning.data.model.LessonDifficulty
import kotlinx.coroutines.tasks.await
import org.json.JSONObject
import java.io.IOException
import java.util.Date

/**
 * Utility class to populate Firebase Firestore with sample lesson data
 * This is for development/testing purposes only
 */
class FirebaseLessonPopulator {
    
    companion object {
        private const val TAG = "FirebaseLessonPopulator"
        private const val LESSONS_FILE = "lessons.json"
    }
    
    private val db = Firebase.firestore
    
    /**
     * Populate Firebase with lesson data from assets/lessons.json
     * This creates Lesson documents in the "lessons" collection
     */
    suspend fun populateLessonData(context: Context): Result<Unit> {
        return try {
            Log.d(TAG, "Starting lesson data population...")
            
            // Load lesson data from assets
            val lessonsData = loadLessonsFromAssets(context)
            if (lessonsData.isEmpty()) {
                return Result.failure(Exception("No lesson data found in assets"))
            }
            
            var totalLessonsAdded = 0
            
            // Process each lesson
            lessonsData.forEach { lessonData ->
                val lesson = createLessonFromJson(lessonData)
                
                // Add to Firebase
                try {
                    db.collection("lessons")
                        .document(lesson.id)
                        .set(lesson)
                        .await()
                    
                    totalLessonsAdded++
                    Log.d(TAG, "Added lesson: ${lesson.title} (ID: ${lesson.id})")
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to add lesson: ${lesson.title}", e)
                }
            }
            
            Log.d(TAG, "Successfully populated $totalLessonsAdded lessons to Firebase")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error populating lesson data", e)
            Result.failure(e)
        }
    }
    
    /**
     * Load lesson data from assets/lessons.json
     */
    private fun loadLessonsFromAssets(context: Context): List<JSONObject> {
        return try {
            val jsonString = context.assets.open(LESSONS_FILE).bufferedReader().use { it.readText() }
            val jsonObject = JSONObject(jsonString)
            val lessonsArray = jsonObject.getJSONArray("lessons")
            
            val result = mutableListOf<JSONObject>()
            for (i in 0 until lessonsArray.length()) {
                result.add(lessonsArray.getJSONObject(i))
            }
            
            result
        } catch (e: IOException) {
            Log.e(TAG, "Error loading lessons from assets", e)
            emptyList()
        }
    }
    
    /**
     * Create a Lesson object from JSON data
     */
    private fun createLessonFromJson(jsonObject: JSONObject): Lesson {
        return Lesson(
            id = jsonObject.getString("id"),
            courseId = "", // Not used in current implementation
            title = jsonObject.getString("title"),
            lessonNumber = jsonObject.getInt("lessonNumber"),
            description = jsonObject.getString("description"),
            duration = "10 min", // Default duration
            difficulty = LessonDifficulty.EASY, // Default difficulty
            totalPoints = jsonObject.optInt("totalPoints", 100),
            wordIds = emptyList(), // Will be populated by vocabulary system
            exercises = listOf("vocabulary", "flashcard"), // Default exercises
            videoUrl = "",
            audioUrl = "",
            imageRes = jsonObject.optInt("imageRes", 0),
            imageUrl = jsonObject.optString("imageUrl", ""),
            isActive = true,
            createdAt = Date(),
            isStarted = false // This will be determined by UserLessonProgress
        )
    }
    
    /**
     * Clear all lesson data from Firebase (for testing)
     */
    suspend fun clearLessonData(): Result<Unit> {
        return try {
            Log.d(TAG, "Clearing all lesson data from Firebase...")
            
            val snapshot = db.collection("lessons").get().await()
            val batch = db.batch()
            
            snapshot.documents.forEach { document ->
                batch.delete(document.reference)
            }
            
            batch.commit().await()
            Log.d(TAG, "Successfully cleared ${snapshot.documents.size} lessons from Firebase")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing lesson data", e)
            Result.failure(e)
        }
    }
    
    /**
     * Check if lesson data exists in Firebase
     */
    suspend fun checkLessonDataExists(): Result<Boolean> {
        return try {
            val snapshot = db.collection("lessons").limit(1).get().await()
            Result.success(snapshot.documents.isNotEmpty())
        } catch (e: Exception) {
            Log.e(TAG, "Error checking lesson data", e)
            Result.failure(e)
        }
    }
    
    /**
     * Create sample user progress data for testing
     */
    suspend fun createSampleUserProgress(userId: String): Result<Unit> {
        return try {
            Log.d(TAG, "Creating sample user progress for user: $userId")
            
            // Get all lessons
            val lessonsSnapshot = db.collection("lessons").get().await()
            val lessons = lessonsSnapshot.documents.mapNotNull { doc ->
                doc.toObject(Lesson::class.java)?.copy(id = doc.id)
            }
            
            // Create progress for first few lessons
            lessons.take(3).forEachIndexed { index, lesson ->
                val progressId = "${userId}_${lesson.id}"
                val progress = mapOf(
                    "id" to progressId,
                    "userId" to userId,
                    "lessonId" to lesson.id,
                    "isStarted" to true,
                    "isCompleted" to (index == 0), // First lesson completed
                    "currentPoints" to if (index == 0) 100 else (index + 1) * 30,
                    "totalPoints" to 100,
                    "progressPercentage" to if (index == 0) 100 else (index + 1) * 30,
                    "timeSpentMinutes" to (index + 1) * 15,
                    "attempts" to index + 1,
                    "bestScore" to if (index == 0) 100 else (index + 1) * 30,
                    "wordsLearned" to (index + 1) * 5,
                    "totalWords" to 10,
                    "completedExercises" to if (index == 0) listOf("vocabulary", "flashcard") else listOf("vocabulary"),
                    "learnedWordIds" to emptyList<String>(),
                    "startedAt" to Date(),
                    "completedAt" to if (index == 0) Date() else null,
                    "lastAccessedAt" to Date()
                )
                
                db.collection("userLessonProgress")
                    .document(progressId)
                    .set(progress)
                    .await()
                
                Log.d(TAG, "Created progress for lesson: ${lesson.title}")
            }
            
            Log.d(TAG, "Successfully created sample user progress")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error creating sample user progress", e)
            Result.failure(e)
        }
    }
}
