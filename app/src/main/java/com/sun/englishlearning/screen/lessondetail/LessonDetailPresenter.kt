package com.sun.englishlearning.screen.lessondetail

import android.content.Context
import android.util.Log
import com.sun.englishlearning.data.model.Lesson
import com.sun.englishlearning.data.model.Word
import com.sun.englishlearning.data.repository.WordRepository
import com.sun.englishlearning.data.repository.WordRepositoryImpl
import com.sun.englishlearning.utils.FirebaseDataPopulator
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class LessonDetailPresenter : LessonDetailContract.Presenter {

    companion object {
        private const val TAG = "LessonDetailPresenter"
    }

    private var view: LessonDetailContract.View? = null
    private var currentLesson: Lesson? = null
    private var currentVocabulary: List<Word> = emptyList()
    private var context: Context? = null

    // Firebase repository for vocabulary data
    private val wordRepository: WordRepository = WordRepositoryImpl()

    // Coroutine scope for async operations
    private val presenterScope = CoroutineScope(Dispatchers.Main + Job())

    fun setContext(context: Context) {
        this.context = context
    }

    override fun loadLessonDetail(lesson: Lesson) {
        currentLesson = lesson
        view?.displayLessonInfo(lesson)
        loadVocabulary(lesson.id)
    }

    override fun loadVocabulary(lessonId: String) {
        Log.d(TAG, "Loading vocabulary for lesson: $lessonId")
        view?.showLoading()

        presenterScope.launch {
            try {
                // First check if we need to populate Firebase with sample data
                context?.let { ctx ->
                    checkAndPopulateFirebaseData(ctx)
                }

                // Load vocabulary from Firebase
                val result = withContext(Dispatchers.IO) {
                    wordRepository.getWordsByLesson(lessonId)
                }

                result.fold(
                    onSuccess = { words ->
                        Log.d(TAG, "Successfully loaded ${words.size} words from Firebase")
                        currentVocabulary = words
                        view?.hideLoading()

                        if (words.isNotEmpty()) {
                            view?.showVocabulary(words)
                        } else {
                            Log.w(TAG, "No vocabulary found for lesson $lessonId, using sample data")
                            // Fallback to sample data if no words found in Firebase
                            val sampleVocabulary = createSampleVocabulary(lessonId)
                            currentVocabulary = sampleVocabulary
                            view?.showVocabulary(sampleVocabulary)
                        }
                    },
                    onFailure = { exception ->
                        Log.e(TAG, "Error loading vocabulary from Firebase", exception)
                        view?.hideLoading()

                        // Fallback to sample data on error
                        Log.w(TAG, "Falling back to sample vocabulary data")
                        val sampleVocabulary = createSampleVocabulary(lessonId)
                        currentVocabulary = sampleVocabulary
                        view?.showVocabulary(sampleVocabulary)

                        // Show error message but don't block the UI
                        view?.showError("Could not load vocabulary from server. Using offline data.")
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "Unexpected error in loadVocabulary", e)
                view?.hideLoading()
                view?.showError("Error loading vocabulary: ${e.message}")
            }
        }
    }

    /**
     * Check if Firebase has vocabulary data, and populate it if empty
     * This is for development/testing purposes
     */
    private suspend fun checkAndPopulateFirebaseData(context: Context) {
        try {
            val dataPopulator = FirebaseDataPopulator()
            val hasData = dataPopulator.checkVocabularyDataExists()

            hasData.fold(
                onSuccess = { exists ->
                    if (!exists) {
                        Log.d(TAG, "No vocabulary data in Firebase, populating with sample data...")
                        dataPopulator.populateVocabularyData(context).fold(
                            onSuccess = {
                                Log.d(TAG, "Successfully populated Firebase with vocabulary data")
                            },
                            onFailure = { exception ->
                                Log.e(TAG, "Failed to populate Firebase data", exception)
                            }
                        )
                    } else {
                        Log.d(TAG, "Firebase already has vocabulary data")
                    }
                },
                onFailure = { exception ->
                    Log.e(TAG, "Error checking Firebase data existence", exception)
                }
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error in checkAndPopulateFirebaseData", e)
        }
    }
    
    private fun createSampleVocabulary(lessonId: String): List<Word> {
        // Sample vocabulary data - replace with actual data loading
        return listOf(
            Word(
                id = "1",
                word = "Hello",
                definition = "A greeting",
                pronunciation = "/həˈloʊ/",
                phonetic = "həˈloʊ",
                partOfSpeech = "interjection",
                example = "Hello, how are you?",
                lessonId = lessonId
            ),
            Word(
                id = "2", 
                word = "World",
                definition = "The earth and all its inhabitants",
                pronunciation = "/wɜːrld/",
                phonetic = "wɜːrld",
                partOfSpeech = "noun",
                example = "The world is beautiful.",
                lessonId = lessonId
            )
        )
    }

    override fun onBackClicked() {
        view?.navigateBack()
    }

    override fun onWordClicked(word: Word) {
        try {
            android.util.Log.d("LessonDetailPresenter", "Word clicked: ${word.word}")
            android.util.Log.d("LessonDetailPresenter", "Current vocabulary size: ${currentVocabulary.size}")

            // Find the index of the clicked word in the current vocabulary list
            val wordIndex = currentVocabulary.indexOfFirst { it.word == word.word }
            android.util.Log.d("LessonDetailPresenter", "Word index found: $wordIndex")

            if (wordIndex != -1) {
                val lessonTitle = currentLesson?.title ?: ""
                android.util.Log.d("LessonDetailPresenter", "Navigating to flashcard with title: $lessonTitle")
                view?.navigateToFlashcard(currentVocabulary, wordIndex, lessonTitle)
            } else {
                android.util.Log.w("LessonDetailPresenter", "Word index not found, showing word detail")
                // Fallback to showing word detail if index not found
                view?.showWordDetail(word)
            }
        } catch (e: Exception) {
            android.util.Log.e("LessonDetailPresenter", "Error handling word click", e)
            view?.showError("Error opening flashcard: ${e.message}")
        }
    }

    override fun onSoundClicked(word: Word) {
        view?.playWordSound(word)
    }

    override fun attachView(view: LessonDetailContract.View?) {
        this.view = view
    }

    override fun detachView() {
        this.view = null
        // Cancel all coroutines when view is detached
        presenterScope.coroutineContext[Job]?.cancel()
    }

    override fun onStart() {
        // Presenter started - no specific action needed
    }

    override fun onStop() {
        // Presenter stopped - no specific action needed
    }
}
