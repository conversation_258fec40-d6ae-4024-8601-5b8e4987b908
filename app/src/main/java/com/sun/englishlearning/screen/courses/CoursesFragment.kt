package com.sun.englishlearning.screen.courses

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.sun.englishlearning.R
import com.sun.englishlearning.databinding.ActivityLessonsBinding
import com.sun.englishlearning.data.model.Lesson
import androidx.fragment.app.Fragment
import com.sun.englishlearning.screen.courses.adapter.CoursesAdapter

class CoursesFragment : Fragment(), CoursesContract.View {

    private var _viewBinding: ActivityLessonsBinding? = null
    private val viewBinding get() = _viewBinding!!

    private lateinit var coursesAdapter: CoursesAdapter
    private lateinit var presenter: CoursesContract.Presenter
    private var isOngoingTabSelected = true

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        _viewBinding = ActivityLessonsBinding.inflate(inflater, container, false)
        return viewBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initPresenter()
        initView()
    }

    override fun onStart() {
        super.onStart()
        presenter.onStart()
    }

    override fun onStop() {
        super.onStop()
        presenter.onStop()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        presenter.detachView()
        _viewBinding = null
    }

    private fun initPresenter() {
        presenter = CoursesPresenter()
        (presenter as CoursesPresenter).setContext(requireContext())
        presenter.attachView(this)
    }

    private fun initView() {
        setupRecyclerView()
        setupTabs()
        setupBackButton()
        setupDebugFeatures()
    }

    private fun setupDebugFeatures() {
        // Add long click listener to ongoing tab for debug features
        viewBinding.tabOngoing.setOnLongClickListener {
            android.util.Log.d("CoursesFragment", "Debug: Populating Firebase data...")
            Toast.makeText(requireContext(), "Populating Firebase data...", Toast.LENGTH_SHORT).show()
            (presenter as CoursesPresenter).populateFirebaseData()
            true
        }
    }

    private fun setupRecyclerView() {
        coursesAdapter = CoursesAdapter { lesson ->
            presenter.onLessonClicked(lesson)
        }

        viewBinding.recyclerViewLessons.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = coursesAdapter
        }
    }

    private fun setupTabs() {
        // Set initial tab state
        selectTab(true)

        viewBinding.tabOngoing.setOnClickListener {
            presenter.onTabSelected(true)
        }

        viewBinding.tabCompleted.setOnClickListener {
            presenter.onTabSelected(false)
        }
    }

    private fun selectTab(isOngoing: Boolean) {
        isOngoingTabSelected = isOngoing

        if (isOngoing) {
            // Select Ongoing tab
            viewBinding.tabOngoing.isSelected = true
            viewBinding.tabCompleted.isSelected = false
            viewBinding.tabOngoing.setTextColor(ContextCompat.getColor(requireContext(), R.color.tab_text_color))
            viewBinding.tabCompleted.setTextColor(ContextCompat.getColor(requireContext(), R.color.tab_text_inactive))
        } else {
            // Select Completed tab
            viewBinding.tabOngoing.isSelected = false
            viewBinding.tabCompleted.isSelected = true
            viewBinding.tabOngoing.setTextColor(ContextCompat.getColor(requireContext(), R.color.tab_text_inactive))
            viewBinding.tabCompleted.setTextColor(ContextCompat.getColor(requireContext(), R.color.tab_text_color))
        }
    }

    private fun setupBackButton() {
        viewBinding.btnBack.setOnClickListener {
            // Handle back button click
            requireActivity().onBackPressed()
        }
    }

    // MVP View implementations
    override fun showLoading() {
        android.util.Log.d("CoursesFragment", "Showing loading state")
        // Hide RecyclerView and show loading message
        viewBinding.recyclerViewLessons.visibility = View.GONE
        // You can add a progress bar or loading text here if available in layout
    }

    override fun hideLoading() {
        android.util.Log.d("CoursesFragment", "Hiding loading state")
        // Show RecyclerView after loading
        viewBinding.recyclerViewLessons.visibility = View.VISIBLE
    }

    override fun showOngoingLessons(lessons: List<Lesson>) {
        android.util.Log.d("CoursesFragment", "Displaying ${lessons.size} ongoing lessons")
        coursesAdapter.updateLessons(lessons)

        // Ensure RecyclerView is visible
        viewBinding.recyclerViewLessons.visibility = View.VISIBLE
    }

    override fun showCompletedLessons(lessons: List<Lesson>) {
        android.util.Log.d("CoursesFragment", "Displaying ${lessons.size} completed lessons")
        coursesAdapter.updateLessons(lessons)

        // Ensure RecyclerView is visible
        viewBinding.recyclerViewLessons.visibility = View.VISIBLE
    }

    override fun showError(message: String) {
        android.util.Log.e("CoursesFragment", "Error: $message")
        Toast.makeText(requireContext(), message, Toast.LENGTH_LONG).show()

        // Ensure RecyclerView is visible even after error
        viewBinding.recyclerViewLessons.visibility = View.VISIBLE
    }

    override fun navigateToLessonDetail(lesson: Lesson) {
        val action = CoursesFragmentDirections.actionCoursesToLessonDetail(lesson)
        findNavController().navigate(action)
    }

    override fun updateTabSelection(isOngoing: Boolean) {
        selectTab(isOngoing)
    }
}
