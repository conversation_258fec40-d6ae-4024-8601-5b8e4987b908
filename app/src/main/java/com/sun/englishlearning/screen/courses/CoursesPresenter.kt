package com.sun.englishlearning.screen.courses

import android.content.Context
import android.util.Log
import com.sun.englishlearning.data.model.Lesson
import com.sun.englishlearning.data.repository.LessonRepository
import com.sun.englishlearning.data.repository.LessonRepositoryImpl
import com.sun.englishlearning.data.repository.UserLessonProgressRepository
import com.sun.englishlearning.data.repository.UserLessonProgressRepositoryImpl
import com.sun.englishlearning.utils.FirebaseLessonPopulator
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class CoursesPresenter : CoursesContract.Presenter {

    companion object {
        private const val TAG = "CoursesPresenter"
        private const val DEFAULT_USER_ID = "default_user" // For development
    }

    private var view: CoursesContract.View? = null
    private var context: Context? = null
    private var isOngoingTabSelected = true
    private val userProgressRepository: UserLessonProgressRepository = UserLessonProgressRepositoryImpl()
    private val lessonRepository: LessonRepository = LessonRepositoryImpl(userProgressRepository)
    private val presenterScope = CoroutineScope(Dispatchers.Main + Job())

    fun setContext(context: Context) {
        this.context = context
    }

    override fun loadOngoingLessons() {
        Log.d(TAG, "Loading ongoing lessons...")
        view?.showLoading()

        presenterScope.launch {
            try {
                // First check if we need to populate Firebase with sample data
                context?.let { ctx ->
                    checkAndPopulateLessonData(ctx)
                }

                // Get all lessons from Firebase
                val lessonsResult = withContext(Dispatchers.IO) {
                    lessonRepository.getAllLessons()
                }

                lessonsResult.fold(
                    onSuccess = { lessons ->
                        Log.d(TAG, "Successfully loaded ${lessons.size} lessons from Firebase")

                        if (lessons.isNotEmpty()) {
                            // Get user progress to determine ongoing lessons
                            val userId = getCurrentUserId()
                            val progressResult = withContext(Dispatchers.IO) {
                                userProgressRepository.getUserProgressByUser(userId)
                            }

                            progressResult.fold(
                                onSuccess = { progressList ->
                                    val startedLessonIds = progressList
                                        .filter { it.isStarted && !it.isCompleted }
                                        .map { it.lessonId }

                                    val ongoingLessons = lessons.filter { lesson ->
                                        startedLessonIds.contains(lesson.id)
                                    }

                                    Log.d(TAG, "Found ${ongoingLessons.size} ongoing lessons")
                                    view?.hideLoading()
                                    view?.showOngoingLessons(ongoingLessons)
                                },
                                onFailure = { exception ->
                                    Log.e(TAG, "Error loading user progress", exception)
                                    // Show all lessons as fallback
                                    view?.hideLoading()
                                    view?.showOngoingLessons(lessons)
                                }
                            )
                        } else {
                            Log.w(TAG, "No lessons found in Firebase")
                            view?.hideLoading()
                            view?.showOngoingLessons(emptyList())
                        }
                    },
                    onFailure = { exception ->
                        Log.e(TAG, "Error loading lessons from Firebase", exception)
                        view?.hideLoading()
                        view?.showError("Could not load lessons from server: ${exception.message}")
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "Unexpected error in loadOngoingLessons", e)
                view?.hideLoading()
                view?.showError("Error loading lessons: ${e.message}")
            }
        }
    }

    override fun loadCompletedLessons() {
        Log.d(TAG, "Loading completed lessons...")
        view?.showLoading()

        presenterScope.launch {
            try {
                // Get all lessons from Firebase
                val lessonsResult = withContext(Dispatchers.IO) {
                    lessonRepository.getAllLessons()
                }

                lessonsResult.fold(
                    onSuccess = { lessons ->
                        Log.d(TAG, "Successfully loaded ${lessons.size} lessons for completed check")

                        if (lessons.isNotEmpty()) {
                            // Get user progress to determine completed lessons
                            val userId = getCurrentUserId()
                            val progressResult = withContext(Dispatchers.IO) {
                                userProgressRepository.getCompletedLessons(userId)
                            }

                            progressResult.fold(
                                onSuccess = { completedProgressList ->
                                    val completedLessonIds = completedProgressList.map { it.lessonId }
                                    val completedLessons = lessons.filter { lesson ->
                                        completedLessonIds.contains(lesson.id)
                                    }

                                    Log.d(TAG, "Found ${completedLessons.size} completed lessons")
                                    view?.hideLoading()
                                    view?.showCompletedLessons(completedLessons)
                                },
                                onFailure = { exception ->
                                    Log.e(TAG, "Error loading completed progress", exception)
                                    // Show empty list as fallback
                                    view?.hideLoading()
                                    view?.showCompletedLessons(emptyList())
                                }
                            )
                        } else {
                            Log.w(TAG, "No lessons found in Firebase")
                            view?.hideLoading()
                            view?.showCompletedLessons(emptyList())
                        }
                    },
                    onFailure = { exception ->
                        Log.e(TAG, "Error loading lessons from Firebase", exception)
                        view?.hideLoading()
                        view?.showError("Could not load lessons from server: ${exception.message}")
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "Unexpected error in loadCompletedLessons", e)
                view?.hideLoading()
                view?.showError("Error loading lessons: ${e.message}")
            }
        }
    }

    override fun onTabSelected(isOngoing: Boolean) {
        isOngoingTabSelected = isOngoing
        view?.updateTabSelection(isOngoing)

        if (isOngoing) {
            loadOngoingLessons()
        } else {
            loadCompletedLessons()
        }
    }

    override fun onLessonClicked(lesson: Lesson) {
        view?.navigateToLessonDetail(lesson)
    }

    override fun refreshLessons() {
        // Simply reload the current tab since we don't have a cache to refresh
        if (isOngoingTabSelected) {
            loadOngoingLessons()
        } else {
            loadCompletedLessons()
        }
    }

    override fun attachView(view: CoursesContract.View?) {
        this.view = view
    }

    override fun detachView() {
        this.view = null
        // Cancel all coroutines when view is detached
        presenterScope.coroutineContext[Job]?.cancel()
    }

    override fun onStart() {
        // Load initial data
        onTabSelected(true) // Load ongoing lessons by default
    }

    override fun onStop() {
        // Presenter stopped - no specific action needed
    }

    /**
     * Get current user ID - for development purposes using default user
     * In production, this should come from Firebase Auth or session management
     */
    private fun getCurrentUserId(): String {
        // TODO: Implement proper user identification logic
        // This could come from SharedPreferences, Firebase Auth, or session management
        return DEFAULT_USER_ID // Use default user for development
    }

    /**
     * Check if Firebase has lesson data, and populate it if empty
     * This is for development/testing purposes
     */
    private suspend fun checkAndPopulateLessonData(context: Context) {
        try {
            val lessonPopulator = FirebaseLessonPopulator()
            val hasData = lessonPopulator.checkLessonDataExists()

            hasData.fold(
                onSuccess = { exists ->
                    if (!exists) {
                        Log.d(TAG, "No lesson data in Firebase, populating with sample data...")
                        lessonPopulator.populateLessonData(context).fold(
                            onSuccess = {
                                Log.d(TAG, "Successfully populated Firebase with lesson data")
                            },
                            onFailure = { exception ->
                                Log.e(TAG, "Failed to populate Firebase lesson data", exception)
                            }
                        )
                    } else {
                        Log.d(TAG, "Firebase already has lesson data")
                    }
                },
                onFailure = { exception ->
                    Log.e(TAG, "Error checking Firebase lesson data existence", exception)
                }
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error in checkAndPopulateLessonData", e)
        }
    }
}
