<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="@dimen/dp_16"
    app:cardElevation="@dimen/dp_4"
    android:layout_margin="@dimen/dp_4">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <!-- Course Image with Checkmark Overlay -->
        <RelativeLayout
            android:layout_width="@dimen/dp_120"
            android:layout_height="@dimen/dp_120">

            <ImageView
                android:id="@+id/iv_course_image"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/img_ob1"
                android:scaleType="centerCrop" />

            <!-- Checkmark in top-left corner -->
            <LinearLayout
                android:layout_width="@dimen/dp_32"
                android:layout_height="@dimen/dp_32"
                android:layout_margin="@dimen/dp_8"
                android:background="@drawable/bg_blue_button"
                android:gravity="center"
                android:layout_alignParentTop="true"
                android:layout_alignParentStart="true">

                <ImageView
                    android:layout_width="@dimen/dp_16"
                    android:layout_height="@dimen/dp_16"
                    android:src="@drawable/ic_check"
                    app:tint="@android:color/white" />

            </LinearLayout>

        </RelativeLayout>

        <!-- Course Details -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:padding="@dimen/dp_16"
            android:gravity="center_vertical">

            <!-- Course Title -->
            <TextView
                android:id="@+id/tv_course_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="@dimen/sp_16"
                android:textColor="@android:color/black"
                android:textStyle="bold"
                android:layout_marginBottom="@dimen/dp_8"
                android:maxLines="2"
                android:ellipsize="end" />

            <!-- Course Subtitle -->
            <TextView
                android:id="@+id/tv_course_subtitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="@dimen/sp_14"
                android:textColor="@color/black" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
