<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.EnglishLearning">
        <activity
            android:name=".screen.login.LoginActivity"
            android:exported="false" />
        <activity
            android:name=".screen.splash.SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.App.Starting">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".screen.onboarding.OnboardingActivity"
            android:exported="false" />
        <activity
            android:name=".MainActivity"
            android:exported="true">
        </activity>
        <activity
            android:name=".screen.savedwords.SavedWordsActivity"
            android:exported="false" />
        <activity
            android:name=".screen.search.WordSearchActivity"
            android:exported="false" />
        <activity
            android:name=".screen.flashcard.FlashcardActivity"
            android:exported="false" />
    </application>

</manifest>
